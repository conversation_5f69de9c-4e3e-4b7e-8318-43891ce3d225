<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.cm.infrastructure.budget.mapper.CostQuarterlyBudgetMapper">

    <!-- 汇总与指定年度预算相关的已审批通过的季度预算的项目预算总额 -->
    <select id="sumApprovedQuarterlyBudgetRevenueByAnnualBudgetId" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(revenue_budget_amount), 0)
        FROM cost_quarterly_budget qb
        INNER JOIN cost_annual_budget ab ON qb.annual_budget_code = ab.budget_code
        WHERE ab.id = #{annualBudgetId}
          AND qb.budget_status = 'LOCKED'
          AND qb.del_flag = 0
          AND ab.del_flag = 0
          <if test="excludeQuarterlyBudgetId != null and excludeQuarterlyBudgetId != ''">
              AND qb.id != #{excludeQuarterlyBudgetId}
          </if>
    </select>

    <!-- 汇总与指定年度预算和预算科目相关的已审批通过的季度预算的支出金额 -->
    <select id="sumApprovedQuarterlyBudgetExpenditureBySubjectCode" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(sdc.expenditure_budget_amount), 0)
        FROM cost_quarterly_budget qb
        INNER JOIN cost_annual_budget ab ON qb.annual_budget_code = ab.budget_code
        INNER JOIN cost_quarterly_budget_subject_direct_cost sdc ON qb.id = sdc.quarterly_budget_id
        WHERE ab.id = #{annualBudgetId}
          AND qb.budget_status = 'LOCKED'
          AND qb.del_flag = 0
          AND ab.del_flag = 0
          AND sdc.del_flag = 0
          AND sdc.budget_subject_code = #{budgetSubjectCode}
          <if test="excludeQuarterlyBudgetId != null and excludeQuarterlyBudgetId != ''">
              AND qb.id != #{excludeQuarterlyBudgetId}
          </if>
    </select>

</mapper>