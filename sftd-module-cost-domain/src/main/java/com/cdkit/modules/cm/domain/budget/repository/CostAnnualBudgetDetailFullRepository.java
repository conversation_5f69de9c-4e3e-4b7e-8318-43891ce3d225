package com.cdkit.modules.cm.domain.budget.repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 项目年度预算详情完整信息仓储接口
 * <AUTHOR>
 * @date 2025-08-07
 */
public interface CostAnnualBudgetDetailFullRepository {

    /**
     * 根据项目年度预算明细ID查询完整详情信息
     *
     * @param budgetDetailId 项目年度预算明细ID
     * @return 完整详情信息
     */
    CostAnnualBudgetDetailFullInfo queryDetailFullById(String budgetDetailId);

    /**
     * 根据年度预算ID查询完整详情信息
     *
     * @param budgetId 年度预算ID
     * @return 完整详情信息
     */
    AnnualBudgetDetailFullInfo findByBudgetId(String budgetId);

    /**
     * 项目年度预算完整详情信息
     */
    class CostAnnualBudgetDetailFullInfo {
        // 主表信息
        private String id;
        private String budgetId;
        private String planId;
        private String planCode;
        private String projectCode;
        private String projectName;
        private String professionalCompany;
        private String center;
        private String budgetType;
        private String wbsCode;
        private String projectType;
        private String fourthLevelBusiness;
        private String businessArea;
        private BigDecimal revenueBudget;
        private BigDecimal directCostBudget;
        private BigDecimal indirectCostTotal;
        private BigDecimal centerIndirectCostTotal;
        private BigDecimal nonOperatingCenterIndirectCostTotal;
        private BigDecimal comprehensiveManagementIndirectCostTotal;
        private BigDecimal totalCostBudget;
        private BigDecimal netProfit;
        private BigDecimal netProfitRate;
        private BigDecimal grossProfit;
        private BigDecimal grossProfitRate;
        private BigDecimal marginalProfit;
        private BigDecimal marginalProfitRate;
        private Date createTime;
        private String createBy;
        private Date updateTime;
        private String updateBy;
        private Integer tenantId;
        private String sysOrgCode;

        // 子表信息
        private List<DirectCostItem> directCostList;
        private List<CenterIndirectCostItem> centerIndirectCostList;
        private List<ComprehensiveIndirectCostItem> comprehensiveIndirectCostList;
        private List<NonOperatingIndirectCostItem> nonOperatingIndirectCostList;

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }

        public String getBudgetId() { return budgetId; }
        public void setBudgetId(String budgetId) { this.budgetId = budgetId; }

        public String getPlanId() { return planId; }
        public void setPlanId(String planId) { this.planId = planId; }

        public String getPlanCode() { return planCode; }
        public void setPlanCode(String planCode) { this.planCode = planCode; }

        public String getProjectCode() { return projectCode; }
        public void setProjectCode(String projectCode) { this.projectCode = projectCode; }

        public String getProjectName() { return projectName; }
        public void setProjectName(String projectName) { this.projectName = projectName; }

        public String getProfessionalCompany() { return professionalCompany; }
        public void setProfessionalCompany(String professionalCompany) { this.professionalCompany = professionalCompany; }

        public String getCenter() { return center; }
        public void setCenter(String center) { this.center = center; }

        public String getBudgetType() { return budgetType; }
        public void setBudgetType(String budgetType) { this.budgetType = budgetType; }

        public String getWbsCode() { return wbsCode; }
        public void setWbsCode(String wbsCode) { this.wbsCode = wbsCode; }

        public String getProjectType() { return projectType; }
        public void setProjectType(String projectType) { this.projectType = projectType; }

        public String getFourthLevelBusiness() { return fourthLevelBusiness; }
        public void setFourthLevelBusiness(String fourthLevelBusiness) { this.fourthLevelBusiness = fourthLevelBusiness; }

        public String getBusinessArea() { return businessArea; }
        public void setBusinessArea(String businessArea) { this.businessArea = businessArea; }

        public BigDecimal getRevenueBudget() { return revenueBudget; }
        public void setRevenueBudget(BigDecimal revenueBudget) { this.revenueBudget = revenueBudget; }

        public BigDecimal getDirectCostBudget() { return directCostBudget; }
        public void setDirectCostBudget(BigDecimal directCostBudget) { this.directCostBudget = directCostBudget; }

        public BigDecimal getIndirectCostTotal() { return indirectCostTotal; }
        public void setIndirectCostTotal(BigDecimal indirectCostTotal) { this.indirectCostTotal = indirectCostTotal; }

        public BigDecimal getCenterIndirectCostTotal() { return centerIndirectCostTotal; }
        public void setCenterIndirectCostTotal(BigDecimal centerIndirectCostTotal) { this.centerIndirectCostTotal = centerIndirectCostTotal; }

        public BigDecimal getNonOperatingCenterIndirectCostTotal() { return nonOperatingCenterIndirectCostTotal; }
        public void setNonOperatingCenterIndirectCostTotal(BigDecimal nonOperatingCenterIndirectCostTotal) { this.nonOperatingCenterIndirectCostTotal = nonOperatingCenterIndirectCostTotal; }

        public BigDecimal getComprehensiveManagementIndirectCostTotal() { return comprehensiveManagementIndirectCostTotal; }
        public void setComprehensiveManagementIndirectCostTotal(BigDecimal comprehensiveManagementIndirectCostTotal) { this.comprehensiveManagementIndirectCostTotal = comprehensiveManagementIndirectCostTotal; }

        public BigDecimal getTotalCostBudget() { return totalCostBudget; }
        public void setTotalCostBudget(BigDecimal totalCostBudget) { this.totalCostBudget = totalCostBudget; }

        public BigDecimal getNetProfit() { return netProfit; }
        public void setNetProfit(BigDecimal netProfit) { this.netProfit = netProfit; }

        public BigDecimal getNetProfitRate() { return netProfitRate; }
        public void setNetProfitRate(BigDecimal netProfitRate) { this.netProfitRate = netProfitRate; }

        public BigDecimal getGrossProfit() { return grossProfit; }
        public void setGrossProfit(BigDecimal grossProfit) { this.grossProfit = grossProfit; }

        public BigDecimal getGrossProfitRate() { return grossProfitRate; }
        public void setGrossProfitRate(BigDecimal grossProfitRate) { this.grossProfitRate = grossProfitRate; }

        public BigDecimal getMarginalProfit() { return marginalProfit; }
        public void setMarginalProfit(BigDecimal marginalProfit) { this.marginalProfit = marginalProfit; }

        public BigDecimal getMarginalProfitRate() { return marginalProfitRate; }
        public void setMarginalProfitRate(BigDecimal marginalProfitRate) { this.marginalProfitRate = marginalProfitRate; }

        public Date getCreateTime() { return createTime; }
        public void setCreateTime(Date createTime) { this.createTime = createTime; }

        public String getCreateBy() { return createBy; }
        public void setCreateBy(String createBy) { this.createBy = createBy; }

        public Date getUpdateTime() { return updateTime; }
        public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }

        public String getUpdateBy() { return updateBy; }
        public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }

        public Integer getTenantId() { return tenantId; }
        public void setTenantId(Integer tenantId) { this.tenantId = tenantId; }

        public String getSysOrgCode() { return sysOrgCode; }
        public void setSysOrgCode(String sysOrgCode) { this.sysOrgCode = sysOrgCode; }

        public List<DirectCostItem> getDirectCostList() { return directCostList; }
        public void setDirectCostList(List<DirectCostItem> directCostList) { this.directCostList = directCostList; }

        public List<CenterIndirectCostItem> getCenterIndirectCostList() { return centerIndirectCostList; }
        public void setCenterIndirectCostList(List<CenterIndirectCostItem> centerIndirectCostList) { this.centerIndirectCostList = centerIndirectCostList; }

        public List<ComprehensiveIndirectCostItem> getComprehensiveIndirectCostList() { return comprehensiveIndirectCostList; }
        public void setComprehensiveIndirectCostList(List<ComprehensiveIndirectCostItem> comprehensiveIndirectCostList) { this.comprehensiveIndirectCostList = comprehensiveIndirectCostList; }

        public List<NonOperatingIndirectCostItem> getNonOperatingIndirectCostList() { return nonOperatingIndirectCostList; }
        public void setNonOperatingIndirectCostList(List<NonOperatingIndirectCostItem> nonOperatingIndirectCostList) { this.nonOperatingIndirectCostList = nonOperatingIndirectCostList; }
    }

    /**
     * 直接成本项
     */
    class DirectCostItem {
        private String id;
        private String budgetDetailId;
        private String subjectCode;
        private String subjectName;
        private String subjectDescription;
        private BigDecimal budgetAmount;
        private Date createTime;
        private String createBy;
        private Date updateTime;
        private String updateBy;
        private Integer tenantId;
        private String sysOrgCode;

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }

        public String getBudgetDetailId() { return budgetDetailId; }
        public void setBudgetDetailId(String budgetDetailId) { this.budgetDetailId = budgetDetailId; }

        public String getSubjectCode() { return subjectCode; }
        public void setSubjectCode(String subjectCode) { this.subjectCode = subjectCode; }

        public String getSubjectName() { return subjectName; }
        public void setSubjectName(String subjectName) { this.subjectName = subjectName; }

        public String getSubjectDescription() { return subjectDescription; }
        public void setSubjectDescription(String subjectDescription) { this.subjectDescription = subjectDescription; }

        public BigDecimal getBudgetAmount() { return budgetAmount; }
        public void setBudgetAmount(BigDecimal budgetAmount) { this.budgetAmount = budgetAmount; }

        public Date getCreateTime() { return createTime; }
        public void setCreateTime(Date createTime) { this.createTime = createTime; }

        public String getCreateBy() { return createBy; }
        public void setCreateBy(String createBy) { this.createBy = createBy; }

        public Date getUpdateTime() { return updateTime; }
        public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }

        public String getUpdateBy() { return updateBy; }
        public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }

        public Integer getTenantId() { return tenantId; }
        public void setTenantId(Integer tenantId) { this.tenantId = tenantId; }

        public String getSysOrgCode() { return sysOrgCode; }
        public void setSysOrgCode(String sysOrgCode) { this.sysOrgCode = sysOrgCode; }
    }

    /**
     * 本中心间接成本项
     */
    class CenterIndirectCostItem {
        private String id;
        private String budgetDetailId;
        private String subjectCode;
        private String subjectName;
        private String subjectDescription;
        private BigDecimal budgetAmount;
        private Date createTime;
        private String createBy;
        private Date updateTime;
        private String updateBy;
        private Integer tenantId;
        private String sysOrgCode;

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }

        public String getBudgetDetailId() { return budgetDetailId; }
        public void setBudgetDetailId(String budgetDetailId) { this.budgetDetailId = budgetDetailId; }

        public String getSubjectCode() { return subjectCode; }
        public void setSubjectCode(String subjectCode) { this.subjectCode = subjectCode; }

        public String getSubjectName() { return subjectName; }
        public void setSubjectName(String subjectName) { this.subjectName = subjectName; }

        public String getSubjectDescription() { return subjectDescription; }
        public void setSubjectDescription(String subjectDescription) { this.subjectDescription = subjectDescription; }

        public BigDecimal getBudgetAmount() { return budgetAmount; }
        public void setBudgetAmount(BigDecimal budgetAmount) { this.budgetAmount = budgetAmount; }

        public Date getCreateTime() { return createTime; }
        public void setCreateTime(Date createTime) { this.createTime = createTime; }

        public String getCreateBy() { return createBy; }
        public void setCreateBy(String createBy) { this.createBy = createBy; }

        public Date getUpdateTime() { return updateTime; }
        public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }

        public String getUpdateBy() { return updateBy; }
        public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }

        public Integer getTenantId() { return tenantId; }
        public void setTenantId(Integer tenantId) { this.tenantId = tenantId; }

        public String getSysOrgCode() { return sysOrgCode; }
        public void setSysOrgCode(String sysOrgCode) { this.sysOrgCode = sysOrgCode; }
    }

    /**
     * 综合间接成本项
     */
    class ComprehensiveIndirectCostItem {
        private String id;
        private String budgetDetailId;
        private String subjectCode;
        private String subjectName;
        private String subjectDescription;
        private BigDecimal budgetAmount;
        private Date createTime;
        private String createBy;
        private Date updateTime;
        private String updateBy;
        private Integer tenantId;
        private String sysOrgCode;

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }

        public String getBudgetDetailId() { return budgetDetailId; }
        public void setBudgetDetailId(String budgetDetailId) { this.budgetDetailId = budgetDetailId; }

        public String getSubjectCode() { return subjectCode; }
        public void setSubjectCode(String subjectCode) { this.subjectCode = subjectCode; }

        public String getSubjectName() { return subjectName; }
        public void setSubjectName(String subjectName) { this.subjectName = subjectName; }

        public String getSubjectDescription() { return subjectDescription; }
        public void setSubjectDescription(String subjectDescription) { this.subjectDescription = subjectDescription; }

        public BigDecimal getBudgetAmount() { return budgetAmount; }
        public void setBudgetAmount(BigDecimal budgetAmount) { this.budgetAmount = budgetAmount; }

        public Date getCreateTime() { return createTime; }
        public void setCreateTime(Date createTime) { this.createTime = createTime; }

        public String getCreateBy() { return createBy; }
        public void setCreateBy(String createBy) { this.createBy = createBy; }

        public Date getUpdateTime() { return updateTime; }
        public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }

        public String getUpdateBy() { return updateBy; }
        public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }

        public Integer getTenantId() { return tenantId; }
        public void setTenantId(Integer tenantId) { this.tenantId = tenantId; }

        public String getSysOrgCode() { return sysOrgCode; }
        public void setSysOrgCode(String sysOrgCode) { this.sysOrgCode = sysOrgCode; }
    }

    /**
     * 非经营间接成本项
     */
    class NonOperatingIndirectCostItem {
        private String id;
        private String budgetDetailId;
        private String subjectCode;
        private String subjectName;
        private String subjectDescription;
        private BigDecimal budgetAmount;
        private Date createTime;
        private String createBy;
        private Date updateTime;
        private String updateBy;
        private Integer tenantId;
        private String sysOrgCode;

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }

        public String getBudgetDetailId() { return budgetDetailId; }
        public void setBudgetDetailId(String budgetDetailId) { this.budgetDetailId = budgetDetailId; }

        public String getSubjectCode() { return subjectCode; }
        public void setSubjectCode(String subjectCode) { this.subjectCode = subjectCode; }

        public String getSubjectName() { return subjectName; }
        public void setSubjectName(String subjectName) { this.subjectName = subjectName; }

        public String getSubjectDescription() { return subjectDescription; }
        public void setSubjectDescription(String subjectDescription) { this.subjectDescription = subjectDescription; }

        public BigDecimal getBudgetAmount() { return budgetAmount; }
        public void setBudgetAmount(BigDecimal budgetAmount) { this.budgetAmount = budgetAmount; }

        public Date getCreateTime() { return createTime; }
        public void setCreateTime(Date createTime) { this.createTime = createTime; }

        public String getCreateBy() { return createBy; }
        public void setCreateBy(String createBy) { this.createBy = createBy; }

        public Date getUpdateTime() { return updateTime; }
        public void setUpdateTime(Date updateTime) { this.updateTime = updateTime; }

        public String getUpdateBy() { return updateBy; }
        public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }

        public Integer getTenantId() { return tenantId; }
        public void setTenantId(Integer tenantId) { this.tenantId = tenantId; }

        public String getSysOrgCode() { return sysOrgCode; }
        public void setSysOrgCode(String sysOrgCode) { this.sysOrgCode = sysOrgCode; }
    }

    /**
     * 年度预算完整详情信息（用于实时计算）
     */
    class AnnualBudgetDetailFullInfo {
        // 主表信息
        private String budgetId;
        private BigDecimal revenueTotalAmount;
        private BigDecimal directCostTotalAmount;

        // 子表信息
        private List<DirectCostItem> directCostList;
        private List<CenterIndirectCostItem> centerIndirectCostList;
        private List<ComprehensiveIndirectCostItem> comprehensiveIndirectCostList;
        private List<NonOperatingIndirectCostItem> nonOperatingIndirectCostList;

        // Getters and Setters
        public String getBudgetId() { return budgetId; }
        public void setBudgetId(String budgetId) { this.budgetId = budgetId; }

        public BigDecimal getRevenueTotalAmount() { return revenueTotalAmount; }
        public void setRevenueTotalAmount(BigDecimal revenueTotalAmount) { this.revenueTotalAmount = revenueTotalAmount; }

        public BigDecimal getDirectCostTotalAmount() { return directCostTotalAmount; }
        public void setDirectCostTotalAmount(BigDecimal directCostTotalAmount) { this.directCostTotalAmount = directCostTotalAmount; }

        public List<DirectCostItem> getDirectCostList() { return directCostList; }
        public void setDirectCostList(List<DirectCostItem> directCostList) { this.directCostList = directCostList; }

        public List<CenterIndirectCostItem> getCenterIndirectCostList() { return centerIndirectCostList; }
        public void setCenterIndirectCostList(List<CenterIndirectCostItem> centerIndirectCostList) { this.centerIndirectCostList = centerIndirectCostList; }

        public List<ComprehensiveIndirectCostItem> getComprehensiveIndirectCostList() { return comprehensiveIndirectCostList; }
        public void setComprehensiveIndirectCostList(List<ComprehensiveIndirectCostItem> comprehensiveIndirectCostList) { this.comprehensiveIndirectCostList = comprehensiveIndirectCostList; }

        public List<NonOperatingIndirectCostItem> getNonOperatingIndirectCostList() { return nonOperatingIndirectCostList; }
        public void setNonOperatingIndirectCostList(List<NonOperatingIndirectCostItem> nonOperatingIndirectCostList) { this.nonOperatingIndirectCostList = nonOperatingIndirectCostList; }
    }
}
