package com.cdkit.modules.cm.domain.budget.service;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 季度预算实时计算请求（领域内部数据结构）
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
public class RealTimeCalculationRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**季度预算ID（可选，新建时为空）*/
    private String quarterlyBudgetId;

    /**季度计划ID（必填）*/
    private String quarterlyPlanId;

    /**年度预算ID（必填）*/
    private String annualBudgetId;

    /**项目收入预算总额（不含税，元）*/
    private BigDecimal projectRevenueBudgetTotal;

    /**采办包明细变更数据*/
    private List<ProcurementPackageDetail> procurementPackageDetails;
}
